swagger.status.200=OK
swagger.status.201=Os dados foram criados com sucesso.
swagger.status.204=AÃ§Ã£o foi realizada com sucesso, porÃ©m nÃ£o hÃ¡ conteÃºdo a ser exibido.
swagger.status.400=Erro ocorrido devido hÃ¡ alguma informaÃ§Ã£o incorreta do usuÃ¡rio.
swagger.status.401=UsuÃ¡rio nÃ£o reconhecido ou expirado.
swagger.status.403=Sem permissÃ£o para acessar o mÃ©todo.
swagger.status.404=NÃ£o foi possÃ­vel encontrar a informaÃ§Ã£o pesquisada.
swagger.status.417=Estavam sendo esperadas algumas informaÃ§Ãµes no cabeÃ§alho, mas nÃ£o foram econtradas.
swagger.status.500=Um erro interno aconteceu e nÃ£o foi trato. SerÃ¡ necessÃ¡rio verificar os logs no ELK.
swagger.status.501=Recurso/mÃ©todo nÃ£o implementado.
swagger.status.502=O recurso externo procurado (API, Micros ServiÃ§os ou Getways) estÃ£o impossibilitados de retornar os dados.
swagger.status.504=Excedido o tempo limite da requisiÃ§Ã£o.