#SUCCESS
structure.process.finished.ok=æä½ã¯æ­£å¸¸ã«å®è¡ããã¾ããã
structure.data.duplicated=ãã¼ã¿ãéè¤ãã¦ãã¾ãã

#FAILURES
structure.process.finished.error=æä½ãå®è¡ã§ãã¾ããã§ããã
structure.resource.not-found=ãªã½ã¼ã¹ãè¦ã¤ããã¾ããã
structure.database.connection.failed=ãã¼ã¿ãã¼ã¹{0}ã¸ã®æ¥ç¶ã«å¤±æãã¾ããã
structure.data.not-found=ãã¼ã¿ãè¦ã¤ããã¾ããã
structure.data.internal.server.error=åé¨ãµã¼ãã¼ã¨ã©ã¼ãçºçãã¾ããã
structure.callserivce.connection-error=å¤é¨å¼ã³åºãã®çºä¿¡ä¸­ã«ã¨ã©ã¼ãçºçãã¾ããã