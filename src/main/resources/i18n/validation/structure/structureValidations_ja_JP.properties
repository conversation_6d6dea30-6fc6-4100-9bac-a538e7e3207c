#####################################################################################################################################
# Structure Internationalization of Spring's validation.    																		#
#####################################################################################################################################

javax.validation.constraints.AssertTrue.message=åé $field çå®ã§ãªããã°ãªããªã
javax.validation.constraints.AssertFalse.message=åé $field falseã§ããå¿è¦ãããã¾ãã
javax.validation.constraints.DecimalMax.message=åé $field æªæºã§ããå¿è¦ãããã¾ã {value}, ãããã${validatedValue} å¤ã¯éç¥ããã¾ããã.
javax.validation.constraints.DecimalMin.message=åé $field ä»¥ä¸ã§ããå¿è¦ãããã¾ã {value}, ãããã${validatedValue} å¤ã¯éç¥ããã¾ããã.
javax.validation.constraints.Digits.message=åé $field æã¤å¿è¦ãããã¾ã {integer} æ´æ° {fraction} åæ°.
javax.validation.constraints.Email.message=åé $field æå¹ãªé»å­ã¡ã¼ã«ã§ããå¿è¦ãããã¾ã, ãããã${validatedValue} å¤ã¯éç¥ããã¾ããã.
javax.validation.constraints.Future.message=åé $field å°æ¥ã®æ¥ä»ã§ããå¿è¦ãããã¾ã, ãããã${validatedValue} å¤ã¯éç¥ããã¾ããã.
javax.validation.constraints.FutureOrPresent.message=åé $field ç¾å¨ã¾ãã¯å°æ¥ã®æ¥ä»ã§ããå¿è¦ãããã¾ã, ãããã${validatedValue} å¤ã¯éç¥ããã¾ããã.
javax.validation.constraints.Max.message=åé $field ä»¥ä¸ã§ãªããã°ãªãã¾ãã {value}, ãããã${validatedValue} å¤ã¯éç¥ããã¾ããã.
javax.validation.constraints.Min.message=åé $field ä»¥ä¸ã§ãªããã°ãªãã¾ãã {value}, ãããã${validatedValue} å¤ã¯éç¥ããã¾ããã.
javax.validation.constraints.Negative.message=åé $field è² ã§ããå¿è¦ãããã¾ãã
javax.validation.constraints.NegativeOrZero.message=åé $field ã¼ã­ä»¥ä¸ã§ããå¿è¦ãããã¾ãã
javax.validation.constraints.NotBlank.message=åé $field ç©ºã«ãããã¨ã¯ã§ãã¾ããã
javax.validation.constraints.NotEmpty.message=åé $field ç©ºã¾ãã¯nullã«ãããã¨ã¯ã§ãã¾ããã
javax.validation.constraints.NotNull.message=åé $field nullã«ãããã¨ã¯ã§ãã¾ããã
javax.validation.constraints.Past.message=åé $field éå»ã®æ¥ä»ã§ããå¿è¦ãããã¾ã, ãããã${validatedValue} å¤ã¯éç¥ããã¾ããã.
javax.validation.constraints.PastOrPresent.message=åé $field å°æ¥ã¾ãã¯éå»ã®æ¥ä»ã§ããå¿è¦ãããã¾ã, ãããã${validatedValue} å¤ã¯éç¥ããã¾ããã.
javax.validation.constraints.Pattern.message=åé $field ã®å¤ã«ç¡å¹ãªå¤ ${validatedValue} ãããã¾ãããå¤ã¯æ¬¡ã®ããã«ãªã£ã¦ããå¿è¦ãããã¾ã ({regexp}).
javax.validation.constraints.Positive.message=åé $field æ­£ã§ãªããã°ãªãã¾ãã
javax.validation.constraints.PositiveOrZero.message=åé $field ã¼ã­ä»¥ä¸ã§ããå¿è¦ãããã¾ãã
javax.validation.constraints.Size.message=åé ã®é·ã $field éã«ããå¿è¦ãããã¾ã {min} ã¨ {max} ã­ã£ã©ã¯ã¿ã¼..
org.hibernate.validator.constraints.Length.message=åé ã®é·ã $field ãè¶ãããã¨ã¯ã§ãã¾ãã {max} ã­ã£ã©ã¯ã¿ã¼.
org.hibernate.validator.constraints.Range.message=åé $field éã«ããå¿è¦ãããã¾ã {min} ã¨ {max}, ãããã${validatedValue} å¤ã¯éç¥ããã¾ããã.
br.com.pacto.validation.Cpfcnpj.message=åé $field æå¹ãªCPF/CNPJã§ããå¿è¦ãããã¾ãã
br.com.pacto.validation.ListValues.message=åé $field ä¾¡å¤ ${validatedValue} ãªã¹ãå¤ã®ç¾å¨ä¾¡å¤ã§ã¯ããã¾ãã {value}
br.com.pacto.validation.Email.message=åé $field æå¹ãªé»å­ã¡ã¼ã«ã§ããå¿è¦ãããã¾ãããå¤ã¯ ${validatedValue} ç¥ããããã
br.com.pacto.validation.Phone.message=åé $field ä¾¡å¤ ${validatedValue} ãã©ã¸ã«ã®é»è©±ã§ã¯ããã¾ããã