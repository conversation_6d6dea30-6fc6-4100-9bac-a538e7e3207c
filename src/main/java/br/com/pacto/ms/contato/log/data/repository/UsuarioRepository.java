package br.com.pacto.ms.contato.log.data.repository;


import br.com.pacto.ms.contato.avulso.data.domain.UsuarioEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.Optional;

public interface UsuarioRepository extends PagingAndSortingRepository<UsuarioEntity, Integer> {

    @Query("SELECT u.codigo FROM UsuarioEntity u WHERE upper(u.username) = upper(:username)")
    Integer getCodigoUsuario(String username);

    @Query("SELECT u FROM UsuarioEntity u WHERE upper(u.username) = upper(:username)")
    Optional<UsuarioEntity> findByUsername(String username);

    @Query("SELECT u FROM UsuarioEntity u WHERE u.username = :loginPactoConversas")
    UsuarioEntity getUsuarioPactoConversa(String loginPactoConversas);
}
