package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.HistoricoPromptEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HistoricoPromptDTO {
    private Integer codigo;
    private String origemCampo;
    private Boolean restore;
    private String conteudo;
    private String nomeUsuario;
    private LocalDateTime criadoEm;

    public static HistoricoPromptDTO toDto(HistoricoPromptEntity historicoPromptEntity) {
        return HistoricoPromptDTO.builder()
                .codigo(historicoPromptEntity.getCodigo())
                .origemCampo(historicoPromptEntity.getOrigemCampo())
                .restore(historicoPromptEntity.getRestore())
                .nomeUsuario(historicoPromptEntity.getUsuario())
                .conteudo(historicoPromptEntity.getConteudo())
                .criadoEm(historicoPromptEntity.getCriadoEm())
                .build();
    }

    public static List<HistoricoPromptDTO> toDto(List<HistoricoPromptEntity> historicoPromptEntities) {
        return historicoPromptEntities.stream().map(HistoricoPromptDTO::toDto).collect(Collectors.toList());
    }
}
