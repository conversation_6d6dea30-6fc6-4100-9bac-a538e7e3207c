package br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain;

import br.com.pacto.ms.contato.log.data.listener.LogListener;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "configuracaocrmia", schema = "public")
@EntityListeners(LogListener.class)
public class ConfiguracaoCrmIAEntity {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo", nullable = false)
    private Integer codigo;

    @Basic
    @Column(name = "codigoempresa")
    private Integer codigoEmpresa;

    @Basic
    @Column(name = "pactoconversaslogin")
    private String loginPactoConversas;

    @Basic
    @Column(name = "pactoconversassenha")
    private String senhaPactoConversas;

    @Basic
    @Column(name = "tokenzapi")
    private String tokenZApi;

    @Basic
    @Column(name = "idinstancia")
    private String idInstanciaZApi;

    @Basic
    @Column(name = "habilitarconfigia")
    private Boolean habilitarconfigia;

    @Basic
    @HistoricoPrompt
    @Column(name = "personalidade")
    private String personalidade;

    @Basic
    @HistoricoPrompt
    @Column(name = "informacoesadicionaisacademia")
    private String informacoesAdicionaisAcademia;

    @Basic
    @Column(name = "horarioPadrao")
    private LocalTime horarioPadrao;

    @Basic
    @Column(name = "whatsappbusiness")
    private Boolean whatsappBusiness;

    @Basic
    @Column(name = "emailresponsavelconversasAI")
    private String emailResponsavelConversasAI;

    @Basic
    @Column(name = "telefoneresponsavelconversasAI")
    private String telefoneResponsavelConversasAI;

    @Basic
    @Column(name = "matriz")
    private Boolean matriz;

    @Basic
    @Column(name = "habilitarconfigpararedeacademias")
    private Boolean habilitarConfigParaRedeAcademias;

    @Basic
    @Column(name = "chavematriz")
    private String chaveMatriz;

    @Basic
    @Column(name = "nomematriz")
    private String nomeMatriz;

    @Embedded
    private ConfiguracaoGymbot configuracaoGymbot;

    @Basic
    @Column
    private String descricaoNotificacaoProativo;

    @Column(name = "desabilitaragendamentoaulasexperimentais")
    private Boolean desabilitarAgendamentoAulasExperimentais;

}
