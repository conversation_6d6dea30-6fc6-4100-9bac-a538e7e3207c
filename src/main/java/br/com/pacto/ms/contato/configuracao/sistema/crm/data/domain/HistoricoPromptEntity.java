package br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain;

import lombok.*;

import javax.persistence.*;
import java.time.LocalDateTime;

@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "historicoPrompt", schema = "public")
public class HistoricoPromptEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @Basic
    @Column(name = "codigoempresa")
    private Integer codigoempresa;

    @Basic
    @Column(name = "origemcampo")
    private String origemCampo;

    @Basic
    @Column(name = "usuario")
    private String usuario;

    @Basic
    @Column(name = "restore")
    private Boolean restore;

    @Basic
    @Column(name = "conteudo")
    private String conteudo;

    @Basic
    @Column(name = "criado_em")
    private LocalDateTime criadoEm;

    @ManyToOne
    @JoinColumn(name = "codigoconfiguracao", referencedColumnName = "codigo")
    private ConfiguracaoCrmIAEntity configuracao;

}
