package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfiguracaoSistemaCrmVO {
    @Schema(description = "Nome do remetente padrão para envio de emails", example = "Chuck Norris")
    private String remetentepadrao;
    @Schema(description = "Email de remetente padrão para envio emails", example = "<EMAIL>")
    private String emailpadrao;
    @Schema(description = "Endereço do servidor de emails", example = "smtp.mailgun.org")
    private String mailserver;
    @Schema(description = "Login do servidor de emails", example = "<EMAIL>")
    private String login;
    @Schema(description = "Senha do servidor de emails", example = "123456")
    private String senha;
    @Schema(description = "Indica se a academia abre aos sábados", example = "true")
    private Boolean abertosabado;
    @Schema(description = "Indica se a academia abre aos domingos", example = "false")
    private Boolean abertodomingo;
    @Schema(description = "Número de faltas permitidos para Plano Mensal (Será considerado os planos de 2 meses)", example = "3")
    private Integer nrfaltaplanomensal;
    @Schema(description = "Número de faltas permitidos para Plano Trimestral (Será considerado os planos de 4 e 5 meses)", example = "3")
    private Integer nrfaltaplanotrimestral;
    @Schema(description = "Número de faltas permitidos para Plano Semestral (Será considerado mais de 5 meses)", example = "3")
    private Integer nrfaltaplanoacimasemestral;
    @Schema(description = "Número de dias para que o sistema comece a identificar cliente que tenham contrato previstos para renovar.", example = "30")
    private Integer nrdiasparaclientepreverenovacao;
    @Schema(description = "(Clientes em situação INATIVO) - Dias após o vencimento do contrato para considerar Desistente", example = "30")
    private Integer nrdiasparaclientepreveperda;
    @Schema(description = "Peso menor ou igual a ser desconsiderado no grupo de risco.", example = "5")
    private Integer nrrisco;
    @Schema(description = "Define se a conexão com servidor de emails utiliza tls ou não.", example = "false")
    private Boolean conexaosegura;
    @Schema(description = "Define se será utilizado associação de tipos de colaboradores a fases do CRM", example = "true")
    private Boolean dividirfase;
    @Schema(description = "Quantidade de dias anteriores a um agendamento para considerar contrato Agendado", example = "15")
    private Integer nrdiasanterioresagendamento;
    @Schema(description = "Quantidade de dias apósum agendamento vencido para ainda considerar contrato Agendado", example = "15")
    private Integer nrdiasposterioresagendamento;
    @Schema(description = "Número de dias limite para agendamento futuro")
    private Integer nrdiaslimiteagendamentofuturo;
    @Schema(description = "Caso você não queira bloquear estes termos, o sistema apenas avisará ao usuário importância de não usá-los no Mailing.", example = "true")
    private Boolean bloqueartermospam;
    @Schema(description = "Url do jenkins para gerenciamento da fila de envio de emails", deprecated = true, example = "http://app.pactosolucoes.com.br/jk")
    private String urljenkins;
    @Schema(description = "Url do micro serviço de envio de emails", deprecated = true, example = "http://app.pactosolucoes.com.br/ms")
    private String urlmailing;
    @Schema(description = "Configuração de conexão com servidor de emails", example = "false")
    private Boolean iniciartls;
    @Schema(description = "Quantidade de indicações de metas por mês por consultor", example = "30")
    private Integer qtdindicacoesmes;
    @Schema(deprecated = true)
    private Integer qtdconversoesvendasmes;
    @Schema(deprecated = true)
    private Integer nrdiasposagendamentoconversaoexaluno;
    @Schema(deprecated = true)
    private Integer qtdconversoesexalunosmes;
    @Schema(description = "Incluir Contratos Renovados em pós vendas", example = "true")
    private Boolean incluircontratosrenovados;
    @Schema(description = "Define se em Clientes ativos sem Professor desconsiderar clientes com vínculo com Professor do TreinoWeb", example = "true")
    private Boolean considerarprofessortreinoweb;
    @Schema(description = "Define se toda ação irá contabilizar meta", example = "true")
    private Boolean batermetatodasacoes;
    @Schema(description = "Define a quantidade de dias para ser uma conversão", example = "20")
    private Short nrdiascontarresultado;
    @Schema(description = "Porta utilizada para conexão com servidor de emails", example = "587")
    private String portaserver;
    @Schema(description = "Define se é obrigatório seguir ordenação das metas", example = "true")
    private Boolean obrigatorioseguirordemmetas;
    @Schema(description = "Define a ordenação das metas do crm por fase. " +
            "Este campo contém a siglas das fases do CRM separadas por vírgula. ", example = "UG,UG,AG,AL,LA,HO,RE,PE,VA,EX,IN,RI,VE,PV,FA,AN,SF,SA,CR,VR,VR")
    private String ordenacaometas;
    @Schema(description = "Ao atribuir true esta configuração, o sistema irá enviar um e-mail para cada destinatário. " +
            "Quando definido como false: o sistema envia um e-mail para cada 100 destinatários em cópia oculta.", example = "false")
    private Boolean enviaremailindividualmente;
    @Schema(description = "Define o email padrão de remetende que será utilizando pelo Mailing", example = "<EMAIL>")
    private Integer remetentepadraomailing;
    @Schema(description = "Informe a quantidade de créditos para que o aluno entre na fase de renovação:\n" +
            " Ex: se definido '7' neste campo, quando o aluno tiver exatamente 7 créditos ou menos, então ele entrará na fase de renovação.\n" +
            " Obs: Este aluno será mostrado na fase renovação juntamente com os alunos que possuem contrato normal (não crédito),\n" +
            " porém será diferenciado dos demais com uma flag 'Contrato de crédito", example = "7")
    private Integer nrcreditostreinorenovar;
    @Schema(description = "Agendamentos devem aparecer na meta do consultor vinculado ao aluno", example = "true")
    private Boolean agendamentoparametaconsultor;
    @Schema(description = "Define se o filtro de colaboradores por tipo de colaborador será utilizado", example = "true")
    private Boolean apresentarcolaboradoresportipocolaborador;
    @Schema(description = "Exibir usuários inativos caso a configuração o Filtro de colaboradores por tipo de colaborador esteja habilitada", example = "false")
    private Boolean apresentarcolaboradoresinativos;
    @Schema(description = "Número de dias para que o sistema identifique clientes que tenham contratos previstos para renovar. \"Contrato maior que um mês\"", example = "30")
    private Integer nrdiasparaclientepreverenovacaomaiorummes;
    @Schema(description = "Define se a conexão com servidor de emails utiliza smtps ou não.", example = "false")
    private Boolean usasmtps;
    @Schema(description = "Endereço do servidor ftp para integração All In", example = "ftp://ftp.pactosolucoes.com.br")
    private String mailingftpserver;
    @Schema(description = "Usuário do servidor ftp para integração All In", example = "pactosolucoes")
    private String mailingftpuser;
    @Schema(description = "Senha do servidor ftp para integração All In", example = "123456")
    private String mailingftppass;
    @Schema(description = "Porta do servidor ftp para integração All In", example = "21")
    private Integer mailingftpport;
    @Schema(description = "Tipo de conexão do servidor ftp para integração All In", example = "ftp")
    private String mailingftptype;
    @Schema(description = "Pasta do servidor ftp para integração All In", example = "/pacto")
    private String mailingftpfolder;
    @Schema(description = "Token para integração com serviço Bitly", example = "segredo")
    private String tokenbitly;
    @Schema(description = "Define se o contrato autorrenovável entra na fase renovação", example = "true")
    private Boolean autorrenovavelentrarenovacao;
    @Schema(deprecated = true)
    private Boolean integracaosendyativa;
    @Schema(description = "Define se vai enviar e-mails via Wagi", example = "false")
    private Boolean integracaopacto;
    @Schema(description = "Limite diário de e-mails", example = "1000")
    private Integer limitediarioemails;
    @Schema(description = "Limite mensal de e-mails", example = "10000")
    private Integer limitemensalpacto;
    @Schema(description = "Define se será utilizado a configuração remetentepadrao para envio de emails", example = "true")
    private Boolean usarremetentepadraogeral;
    @Schema(description = "Define se gerar indicação para cadastros feitos por meio do link de convite no Vendas Online", example = "true")
    private Boolean gerarindicacaoparacadastroconvidadosvendasonline;
}
