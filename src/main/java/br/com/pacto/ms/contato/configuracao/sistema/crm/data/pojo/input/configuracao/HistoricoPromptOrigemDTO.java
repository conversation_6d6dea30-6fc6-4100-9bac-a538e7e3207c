package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HistoricoPromptOrigemDTO {

    @NotNull(message = "Código da empresa é obrigatório")
    private Integer codigoEmpresa;

    @NotNull(message = "Código da configuração é obrigatório")
    private Integer codigoConfiguracao;

    @NotBlank(message = "Campo de origem é obrigatório")
    private String origemCampo;

    private String conteudo;


}
