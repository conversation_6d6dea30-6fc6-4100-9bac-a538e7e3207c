package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmIAEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoGymbot;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoRedeIAEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalTime;
import java.util.Objects;
import java.util.Optional;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConfiguracaoCrmIADTO {

    private Integer codigo;
    @Schema(description = "Codigo da empresa para a configuração", example = "1")
    private Integer codigoEmpresa;
    @Schema(description = "Personalidade da configuração", example = "Personalidade da configuração")
    private String personalidade;
    @Schema(description = "Habilitar  configuração", example = "true")
    private boolean habilitarconfigia;
    @Schema(description = "Informacoes adicionais academia", example = "informacoes adicionais academia")
    private String informacoesAdicionaisAcademia;
    @Schema(description = "Login pacto conversas", example = "pactobr")
    private String loginPactoConversas;
    @Schema(description = "Senha para pacto conversas", example = "******")
    private String senhaPactoConversas;
    @Schema(description = "Token instancia pacto conversas", example = "A35A068BF220488EB14CB75A2F511761")
    private String tokenZApi;
    @Schema(description = "Instancia token z api", example = "3D93CE54E2B640BCE11CC61BF7AC92D7")
    private String idInstanciaZApi;
    @Schema(description = "Horario padrao", example = "2021-07-01T00:00:00")
    private LocalTime horarioPadrao;
    @Schema(description = "Horario padrao", example = "2021-07-01T00:00:00")
    private LocalTime horarioPadraoAnterior;
    @Schema(description = "Whatsapp business", example = "whatsapp business")
    private Boolean whatsappBusiness;
    @Schema(description = "Email configuracao conversas IA", example = "<EMAIL>")
    private String emailResponsavelConversasAI;
    @Schema(description = "Telefone configuracao conversas IA", example = "(xx) xxxx - xxxx")
    private String telefoneResponsavelConversasAI;
    @Schema(description = "Indica que é uma academia filias e que a outra academia da rede ira gerenciar as credenciais", example = "false")
    private Boolean matriz;
    @Schema(description = "Chave matriz", example = "A35A068BF220488EB14CB75A2F511761")
    private String chaveMatriz;
    @Schema(description = "Chave matriz", example = "A35A068BF220488EB14CB75A2F511761")
    private Integer unidadeMatriz;
    @Schema(description = "Nome matriz", example = "Matriz")
    private String nomeMatriz;
    @Schema(description = "Essas são ad configa'", example = "false")
    private ConfiguracaoRedeIADTO configuracaoRede;
    @Schema(description = "Configuracaoes integracao Gymbot", example = "object")
    private ConfiguracaoGymbotDTO configuracaoGymbot;
    @Schema(description = "descricao Notificacao proativo", example = "")
    private String descricaoNotificacaoProativo;
    @Schema(description = "Defini se o método irá propagar o contexto para toda a rede", example = "true")
    private Boolean enviarContextoDeRede = true;
    @Schema(description = "Defini se o método irá ser rede", example = "false")
    private Boolean rede = false;
    private Boolean desabilitarAgendamentoAulasExperimentais;

    public static ConfiguracaoCrmIADTO toDto(ConfiguracaoCrmIAEntity entity) {
        return ConfiguracaoCrmIADTO.builder()
                .codigo(entity.getCodigo())
                .codigoEmpresa(entity.getCodigoEmpresa())
                .habilitarconfigia(!Objects.isNull(entity.getHabilitarconfigia()) && entity.getHabilitarconfigia())
                .informacoesAdicionaisAcademia(entity.getInformacoesAdicionaisAcademia())
                .personalidade(entity.getPersonalidade())
                .loginPactoConversas(entity.getLoginPactoConversas())
                .senhaPactoConversas(entity.getSenhaPactoConversas())
                .tokenZApi(entity.getTokenZApi())
                .idInstanciaZApi(entity.getIdInstanciaZApi())
                .whatsappBusiness(entity.getWhatsappBusiness())
                .horarioPadrao(entity.getHorarioPadrao())
                .emailResponsavelConversasAI(entity.getEmailResponsavelConversasAI())
                .build();
    }

    public static ConfiguracaoCrmIADTO toDto(ConfiguracaoCrmIAEntity entity, ConfiguracaoRedeIAEntity configuracaoRedeIAEntity) {
        return ConfiguracaoCrmIADTO.builder()
                .codigo(entity.getCodigo())
                .codigoEmpresa(entity.getCodigoEmpresa())
                .habilitarconfigia(!Objects.isNull(entity.getHabilitarconfigia()) && entity.getHabilitarconfigia())
                .informacoesAdicionaisAcademia(entity.getInformacoesAdicionaisAcademia())
                .personalidade(entity.getPersonalidade())
                .loginPactoConversas(entity.getLoginPactoConversas())
                .senhaPactoConversas(entity.getSenhaPactoConversas())
                .tokenZApi(entity.getTokenZApi())
                .idInstanciaZApi(entity.getIdInstanciaZApi())
                .whatsappBusiness(entity.getWhatsappBusiness())
                .horarioPadrao(entity.getHorarioPadrao())
                .emailResponsavelConversasAI(entity.getEmailResponsavelConversasAI())
                .telefoneResponsavelConversasAI(entity.getTelefoneResponsavelConversasAI())
                .matriz(entity.getMatriz())
                .chaveMatriz(entity.getChaveMatriz())
                .nomeMatriz(entity.getNomeMatriz())
                .configuracaoRede(ConfiguracaoRedeIADTO.builder()
                        .codigo(configuracaoRedeIAEntity.getCodigo())
                        .chaveBancoMatriz(configuracaoRedeIAEntity.getChaveBancoMatriz())
                        .codigoUnidadeMatriz(configuracaoRedeIAEntity.getCodigoUnidadeMatriz())
                        .tipoConfigRede(configuracaoRedeIAEntity.getTipoConfigRede())
                        .build())
                .configuracaoGymbot(
                        entity.getConfiguracaoGymbot() != null ?
                                ConfiguracaoGymbotDTO.builder()
                                        .habilitarGymbot(entity.getConfiguracaoGymbot().getHabilitarGymbot())
                                        .descricaoDepartamento(entity.getConfiguracaoGymbot().getDescricaoDepartamento())
                                        .idDepartamento(entity.getConfiguracaoGymbot().getIdDepartamento())
                                        .tokenGymbot(entity.getConfiguracaoGymbot().getTokenGymbot())
                                        .build() : null
                )
                .descricaoNotificacaoProativo(entity.getDescricaoNotificacaoProativo())
                .desabilitarAgendamentoAulasExperimentais(entity.getDesabilitarAgendamentoAulasExperimentais())
                .build();
    }
}
