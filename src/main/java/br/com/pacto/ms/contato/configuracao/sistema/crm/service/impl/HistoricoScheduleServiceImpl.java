package br.com.pacto.ms.contato.configuracao.sistema.crm.service.impl;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.HistoricoPromptRepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.HistoricoScheduleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class HistoricoScheduleServiceImpl implements HistoricoScheduleService {

    @Autowired
    private HistoricoPromptRepository repository;

    @Override
    @Transactional
    public int limparHistoricoAntigo(LocalDateTime dataLimite) {
        return repository.deleteBycriadoEmBefore(dataLimite);
    }
}
