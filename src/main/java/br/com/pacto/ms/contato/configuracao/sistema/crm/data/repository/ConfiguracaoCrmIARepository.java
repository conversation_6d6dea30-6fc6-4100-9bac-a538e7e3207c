package br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmIAEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;

public interface ConfiguracaoCrmIARepository extends PagingAndSortingRepository<ConfiguracaoCrmIAEntity, Integer> {

    List<ConfiguracaoCrmIAEntity> findAll();

    @Query("SELECT c FROM ConfiguracaoCrmIAEntity c")
    Optional<List<ConfiguracaoCrmIAEntity>> findAllOptional();

    @Query("SELECT c FROM ConfiguracaoCrmIAEntity c WHERE c.habilitarconfigia = true")
    Optional<List<ConfiguracaoCrmIAEntity>> findAllActivatedOptional();

    @Query("SELECT c FROM ConfiguracaoCrmIAEntity c WHERE c.codigoEmpresa = :codigoEmpresa order by c.codigo desc")
    Optional<List<ConfiguracaoCrmIAEntity>> obterPorCodigoEmpresa(Integer codigoEmpresa);

    @Query("SELECT c FROM ConfiguracaoCrmIAEntity c where c.matriz = true order by c.codigo desc ")
    Optional<List<ConfiguracaoCrmIAEntity>> obterMatriz();

    @Query("SELECT c FROM ConfiguracaoCrmIAEntity c WHERE c.codigoEmpresa = :codigoEmpresa and c.codigo = :codigo order by c.codigo asc")
    Optional<ConfiguracaoCrmIAEntity> findByCodigoEmpresaAndCodigo(Integer codigoEmpresa, Integer codigo);

    @Query("SELECT c FROM ConfiguracaoCrmIAEntity c WHERE c.codigoEmpresa = :codigoEmpresa order by c.codigo desc")
    Optional<List<ConfiguracaoCrmIAEntity>> findByCodigoEmpresa(Integer codigoEmpresa);

}
