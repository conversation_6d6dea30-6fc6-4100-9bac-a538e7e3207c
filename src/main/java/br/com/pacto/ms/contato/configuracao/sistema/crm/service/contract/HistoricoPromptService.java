package br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmIAEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoCrmIADTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.HistoricoPromptDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.HistoricoPromptOrigemDTO;
import java.util.List;

public interface HistoricoPromptService {

    void salvarHistoricoPromptsConfiguracao(ConfiguracaoCrmIAEntity configuracaoCrmIAEntity, ConfiguracaoCrmIAEntity configuracaoAnterior);

    List<HistoricoPromptDTO> consultar(Integer codigoEmpresa, Integer codigoConfiguracao);

    ConfiguracaoCrmIADTO restaurarOrigemHistorico(HistoricoPromptOrigemDTO historicoPromptOrigemDTO);

}
